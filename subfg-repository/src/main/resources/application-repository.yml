spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************
    username: root
    password: 123456

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      # password:
      database: 0
      timeout: 5000ms
      # 连接池配置
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 20
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 2

    # 集群配置（如果使用集群模式）
    # cluster:
    #   nodes:
    #     - 127.0.0.1:7001
    #     - 127.0.0.1:7002
    #     - 127.0.0.1:7003
    #   max-redirects: 3
    # 哨兵配置（如果使用哨兵模式）
    # sentinel:
    #   master: mymaster
    #   nodes:
    #     - 127.0.0.1:26379
    #     - 127.0.0.1:26380
    #     - 127.0.0.1:26381

  # 缓存配置
  cache:
    type: redis
    redis:
      # 缓存过期时间
      time-to-live: 600000ms
      # 是否缓存空值
      cache-null-values: false
      # 键前缀
      key-prefix: "subfg:"
      # 是否使用键前缀
      use-key-prefix: true

  # 邮件配置
  mail:
    host: smtpdm.aliyun.com
    port: 465
    username: <EMAIL>
    password: 27QstR4GRAC3gKG
    default-encoding: UTF-8
    protocol: smtps

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启 SQL 日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 逻辑删除字段
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 主键策略
      id-type: auto
  # Mapper XML 文件位置
  mapper-locations: classpath*:/mapper/**/*.xml
  # 实体类别名包路径
  type-aliases-package: com.subfg.domain.entity

logging:
  level:
    "[com.subfg.repository.mapper]": DEBUG
