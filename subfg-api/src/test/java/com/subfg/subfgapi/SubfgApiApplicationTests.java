package com.subfg.subfgapi;

import com.subfg.common.service.EmailService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class SubfgApiApplicationTests {

    @Autowired
    private EmailService emailService;

    @Test
    void contextLoads() {
    }

    @Test
    void test1(){
        emailService.sendVerifyCodeEmail("<EMAIL>","123456",1);
    }

}
