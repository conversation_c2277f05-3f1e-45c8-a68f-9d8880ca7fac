package com.subfg.subfgapi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.subfg.common.constans.RedisConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.service.EmailService;
import com.subfg.common.util.RedisUtil;
import com.subfg.common.util.VerifyCodeUtil;
import com.subfg.domain.entity.User;
import com.subfg.domain.request.SendEmailCodeReq;
import com.subfg.repository.mapper.UserMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AuthService单元测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthService - 发送邮箱验证码测试")
class AuthServiceTest {

    @Mock
    private UserMapper userMapper;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private EmailService emailService;

    @InjectMocks
    private AuthService authService;

    private SendEmailCodeReq request;
    private User mockUser;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        request = new SendEmailCodeReq();
        request.setEmail("<EMAIL>");
        request.setType(1); // 注册类型

        mockUser = new User();
        mockUser.setUserId("123");
        mockUser.setEmail("<EMAIL>");
    }

    @Test
    @DisplayName("成功发送验证码 - 正常流程")
    void sendEmailCode_Success() {
        // Given
        when(userMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(mockUser);
        when(redisUtil.getString(anyString())).thenReturn(null); // 没有现有验证码
        doNothing().when(redisUtil).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
        doNothing().when(emailService).sendVerifyCodeEmail(anyString(), anyString(), anyInt());

        try (MockedStatic<VerifyCodeUtil> mockedVerifyCodeUtil = mockStatic(VerifyCodeUtil.class)) {
            mockedVerifyCodeUtil.when(() -> VerifyCodeUtil.generateNumberCode(6))
                    .thenReturn("123456");

            // When
            assertDoesNotThrow(() -> authService.sendEmailCode(request));

            // Then
            verify(userMapper).selectOne(any(LambdaQueryWrapper.class));
            verify(redisUtil).getString(RedisConstants.EMAIL_CODE_KEY + request.getEmail());
            verify(redisUtil).set(
                    eq(RedisConstants.EMAIL_CODE_KEY + request.getEmail()),
                    eq("123456"),
                    eq((long) RedisConstants.EMAIL_CODE_EXPIRE_MINUTES),
                    eq(TimeUnit.MINUTES)
            );
            verify(emailService).sendVerifyCodeEmail(request.getEmail(), "123456", request.getType());
        }
    }

    @Test
    @DisplayName("用户不存在 - 抛出业务异常")
    void sendEmailCode_UserNotExists_ThrowsBusinessException() {
        // Given
        when(userMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(
                BusinessException.class,
                () -> authService.sendEmailCode(request)
        );

        assertEquals("user.not.exists", exception.getMessage());
        verify(userMapper).selectOne(any(LambdaQueryWrapper.class));
        verifyNoInteractions(redisUtil, emailService);
    }

    @Test
    @DisplayName("发送频率过快 - 抛出业务异常")
    void sendEmailCode_TooFrequent_ThrowsBusinessException() {
        // Given
        when(userMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(mockUser);
        when(redisUtil.getString(anyString())).thenReturn("existing_code");
        // 模拟剩余时间大于允许的发送间隔（即发送过于频繁）
        long remainingTime = RedisConstants.EMAIL_CODE_EXPIRE_MINUTES * 60 - RedisConstants.EMAIL_CODE_SEND_INTERVAL_SECONDS + 10;
        when(redisUtil.getExpire(anyString(), eq(TimeUnit.SECONDS))).thenReturn(remainingTime);

        // When & Then
        BusinessException exception = assertThrows(
                BusinessException.class,
                () -> authService.sendEmailCode(request)
        );

        assertEquals("email.code.send.too.frequent", exception.getMessage());
        verify(userMapper).selectOne(any(LambdaQueryWrapper.class));
        verify(redisUtil).getString(RedisConstants.EMAIL_CODE_KEY + request.getEmail());
        verify(redisUtil).getExpire(RedisConstants.EMAIL_CODE_KEY + request.getEmail(), TimeUnit.SECONDS);
        verifyNoInteractions(emailService);
    }

    @Test
    @DisplayName("验证码已过期可以重新发送 - 成功")
    void sendEmailCode_ExpiredCode_Success() {
        // Given
        when(userMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(mockUser);
        when(redisUtil.getString(anyString())).thenReturn("existing_code");
        // 模拟剩余时间小于允许的发送间隔（即可以重新发送）
        long remainingTime = RedisConstants.EMAIL_CODE_EXPIRE_MINUTES * 60 - RedisConstants.EMAIL_CODE_SEND_INTERVAL_SECONDS - 10;
        when(redisUtil.getExpire(anyString(), eq(TimeUnit.SECONDS))).thenReturn(remainingTime);
        doNothing().when(redisUtil).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
        doNothing().when(emailService).sendVerifyCodeEmail(anyString(), anyString(), anyInt());

        try (MockedStatic<VerifyCodeUtil> mockedVerifyCodeUtil = mockStatic(VerifyCodeUtil.class)) {
            mockedVerifyCodeUtil.when(() -> VerifyCodeUtil.generateNumberCode(6))
                    .thenReturn("654321");

            // When
            assertDoesNotThrow(() -> authService.sendEmailCode(request));

            // Then
            verify(userMapper).selectOne(any(LambdaQueryWrapper.class));
            verify(redisUtil).getString(RedisConstants.EMAIL_CODE_KEY + request.getEmail());
            verify(redisUtil).getExpire(RedisConstants.EMAIL_CODE_KEY + request.getEmail(), TimeUnit.SECONDS);
            verify(redisUtil).set(
                    eq(RedisConstants.EMAIL_CODE_KEY + request.getEmail()),
                    eq("654321"),
                    eq((long) RedisConstants.EMAIL_CODE_EXPIRE_MINUTES),
                    eq(TimeUnit.MINUTES)
            );
            verify(emailService).sendVerifyCodeEmail(request.getEmail(), "654321", request.getType());
        }
    }

    @Test
    @DisplayName("邮件发送失败 - 抛出业务异常")
    void sendEmailCode_EmailSendFailed_ThrowsBusinessException() {
        // Given
        when(userMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(mockUser);
        when(redisUtil.getString(anyString())).thenReturn(null);
        doNothing().when(redisUtil).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
        doThrow(new RuntimeException("邮件服务异常")).when(emailService)
                .sendVerifyCodeEmail(anyString(), anyString(), anyInt());

        try (MockedStatic<VerifyCodeUtil> mockedVerifyCodeUtil = mockStatic(VerifyCodeUtil.class)) {
            mockedVerifyCodeUtil.when(() -> VerifyCodeUtil.generateNumberCode(6))
                    .thenReturn("123456");

            // When & Then
            BusinessException exception = assertThrows(
                    BusinessException.class,
                    () -> authService.sendEmailCode(request)
            );

            assertEquals("email.send.failed", exception.getMessage());
            verify(userMapper).selectOne(any(LambdaQueryWrapper.class));
            verify(redisUtil).getString(RedisConstants.EMAIL_CODE_KEY + request.getEmail());
            verify(redisUtil).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
            verify(emailService).sendVerifyCodeEmail(anyString(), anyString(), anyInt());
        }
    }

    @Test
    @DisplayName("Redis过期时间为null - 可以发送")
    void sendEmailCode_ExpireTimeNull_Success() {
        // Given
        when(userMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(mockUser);
        when(redisUtil.getString(anyString())).thenReturn("existing_code");
        when(redisUtil.getExpire(anyString(), eq(TimeUnit.SECONDS))).thenReturn(null);
        doNothing().when(redisUtil).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
        doNothing().when(emailService).sendVerifyCodeEmail(anyString(), anyString(), anyInt());

        try (MockedStatic<VerifyCodeUtil> mockedVerifyCodeUtil = mockStatic(VerifyCodeUtil.class)) {
            mockedVerifyCodeUtil.when(() -> VerifyCodeUtil.generateNumberCode(6))
                    .thenReturn("789012");

            // When
            assertDoesNotThrow(() -> authService.sendEmailCode(request));

            // Then
            verify(emailService).sendVerifyCodeEmail(request.getEmail(), "789012", request.getType());
        }
    }

    @Test
    @DisplayName("不同验证码类型测试")
    void sendEmailCode_DifferentTypes_Success() {
        // Given
        when(userMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(mockUser);
        when(redisUtil.getString(anyString())).thenReturn(null);
        doNothing().when(redisUtil).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
        doNothing().when(emailService).sendVerifyCodeEmail(anyString(), anyString(), anyInt());

        try (MockedStatic<VerifyCodeUtil> mockedVerifyCodeUtil = mockStatic(VerifyCodeUtil.class)) {
            mockedVerifyCodeUtil.when(() -> VerifyCodeUtil.generateNumberCode(6))
                    .thenReturn("111111");

            // 测试登录类型
            request.setType(2);
            assertDoesNotThrow(() -> authService.sendEmailCode(request));

            // 测试忘记密码类型
            request.setType(3);
            assertDoesNotThrow(() -> authService.sendEmailCode(request));

            // Then
            verify(emailService, times(2)).sendVerifyCodeEmail(anyString(), anyString(), anyInt());
        }
    }
}
